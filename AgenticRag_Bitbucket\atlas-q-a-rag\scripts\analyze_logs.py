#!/usr/bin/env python3
"""
Log analysis script for the Agentic RAG system.
Provides insights into system performance, errors, and usage patterns.
"""

import argparse
import json
import os
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd


class LogAnalyzer:
    """Analyzes log files and provides insights."""
    
    def __init__(self, log_dir: str = "logs"):
        """
        Initialize log analyzer.
        
        Args:
            log_dir: Directory containing log files
        """
        self.log_dir = Path(log_dir)
        self.stats = defaultdict(int)
        self.errors = []
        self.queries = []
        self.performance_data = []
        
    def analyze_all_logs(self) -> Dict[str, Any]:
        """
        Analyze all log files and return comprehensive report.
        
        Returns:
            Dictionary containing analysis results
        """
        report = {
            "summary": {},
            "errors": {},
            "queries": {},
            "performance": {},
            "tools": {},
            "api": {}
        }
        
        # Analyze different log files
        if (self.log_dir / "app.log").exists():
            report["summary"] = self._analyze_main_logs()
            
        if (self.log_dir / "errors.log").exists():
            report["errors"] = self._analyze_error_logs()
            
        if (self.log_dir / "queries.json").exists():
            report["queries"] = self._analyze_query_logs()
            
        if (self.log_dir / "tools.json").exists():
            report["tools"] = self._analyze_tool_logs()
            
        if (self.log_dir / "api.json").exists():
            report["api"] = self._analyze_api_logs()
            
        if (self.log_dir / "performance.log").exists():
            report["performance"] = self._analyze_performance_logs()
            
        return report
    
    def _analyze_main_logs(self) -> Dict[str, Any]:
        """Analyze main application logs."""
        log_file = self.log_dir / "app.log"
        
        stats = {
            "total_lines": 0,
            "log_levels": Counter(),
            "modules": Counter(),
            "recent_activity": [],
            "time_range": {}
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                stats["total_lines"] = len(lines)
                
                timestamps = []
                for line in lines[-100:]:  # Last 100 lines for recent activity
                    # Parse log line
                    match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (.*?) - (.*?) - (.*)', line)
                    if match:
                        timestamp_str, module, level, message = match.groups()
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        timestamps.append(timestamp)
                        
                        stats["log_levels"][level] += 1
                        stats["modules"][module] += 1
                        
                        if len(stats["recent_activity"]) < 10:
                            stats["recent_activity"].append({
                                "timestamp": timestamp_str,
                                "level": level,
                                "module": module,
                                "message": message[:100] + "..." if len(message) > 100 else message
                            })
                
                if timestamps:
                    stats["time_range"] = {
                        "start": min(timestamps).isoformat(),
                        "end": max(timestamps).isoformat()
                    }
                    
        except Exception as e:
            stats["error"] = f"Failed to analyze main logs: {str(e)}"
            
        return stats
    
    def _analyze_error_logs(self) -> Dict[str, Any]:
        """Analyze error logs."""
        log_file = self.log_dir / "errors.log"
        
        stats = {
            "total_errors": 0,
            "error_types": Counter(),
            "error_modules": Counter(),
            "recent_errors": [],
            "error_patterns": Counter()
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                error_blocks = content.split('\n\n')
                stats["total_errors"] = len([block for block in error_blocks if block.strip()])
                
                for block in error_blocks[-10:]:  # Last 10 errors
                    if not block.strip():
                        continue
                        
                    lines = block.strip().split('\n')
                    if lines:
                        # Parse first line
                        match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - ERROR - (.*?) - (.*)', lines[0])
                        if match:
                            timestamp_str, module, message = match.groups()
                            
                            stats["error_modules"][module] += 1
                            
                            # Categorize error type
                            if "connection" in message.lower():
                                error_type = "Connection Error"
                            elif "timeout" in message.lower():
                                error_type = "Timeout Error"
                            elif "not found" in message.lower():
                                error_type = "Not Found Error"
                            elif "permission" in message.lower():
                                error_type = "Permission Error"
                            else:
                                error_type = "General Error"
                                
                            stats["error_types"][error_type] += 1
                            
                            # Extract error patterns
                            words = message.lower().split()
                            for word in words:
                                if len(word) > 3 and word.isalpha():
                                    stats["error_patterns"][word] += 1
                            
                            stats["recent_errors"].append({
                                "timestamp": timestamp_str,
                                "module": module,
                                "message": message[:200] + "..." if len(message) > 200 else message,
                                "type": error_type
                            })
                            
        except Exception as e:
            stats["error"] = f"Failed to analyze error logs: {str(e)}"
            
        return stats
    
    def _analyze_query_logs(self) -> Dict[str, Any]:
        """Analyze query logs (JSON format)."""
        log_file = self.log_dir / "queries.json"
        
        stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "bots_usage": Counter(),
            "query_patterns": Counter(),
            "average_duration": 0,
            "recent_queries": []
        }
        
        try:
            durations = []
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        stats["total_queries"] += 1
                        
                        if log_entry.get("bot_name"):
                            stats["bots_usage"][log_entry["bot_name"]] += 1
                            
                        if log_entry.get("duration"):
                            durations.append(log_entry["duration"])
                            
                        if log_entry.get("status") == "success":
                            stats["successful_queries"] += 1
                        elif log_entry.get("status") == "failed":
                            stats["failed_queries"] += 1
                            
                        # Add to recent queries
                        if len(stats["recent_queries"]) < 10:
                            stats["recent_queries"].append({
                                "timestamp": log_entry.get("timestamp"),
                                "bot_name": log_entry.get("bot_name"),
                                "query_text": log_entry.get("query_text", "")[:100] + "..." if len(log_entry.get("query_text", "")) > 100 else log_entry.get("query_text", ""),
                                "status": log_entry.get("status", "unknown"),
                                "duration": log_entry.get("duration", 0)
                            })
                            
                    except json.JSONDecodeError:
                        continue
                        
            if durations:
                stats["average_duration"] = sum(durations) / len(durations)
                
        except Exception as e:
            stats["error"] = f"Failed to analyze query logs: {str(e)}"
            
        return stats
    
    def _analyze_tool_logs(self) -> Dict[str, Any]:
        """Analyze tool execution logs."""
        log_file = self.log_dir / "tools.json"
        
        stats = {
            "total_executions": 0,
            "tool_usage": Counter(),
            "tool_performance": defaultdict(list),
            "successful_executions": 0,
            "failed_executions": 0,
            "recent_executions": []
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        stats["total_executions"] += 1
                        
                        tool_name = log_entry.get("tool_name", "unknown")
                        stats["tool_usage"][tool_name] += 1
                        
                        if log_entry.get("duration"):
                            stats["tool_performance"][tool_name].append(log_entry["duration"])
                            
                        if log_entry.get("status") == "success":
                            stats["successful_executions"] += 1
                        elif log_entry.get("status") == "failed":
                            stats["failed_executions"] += 1
                            
                        # Add to recent executions
                        if len(stats["recent_executions"]) < 10:
                            stats["recent_executions"].append({
                                "timestamp": log_entry.get("timestamp"),
                                "tool_name": tool_name,
                                "duration": log_entry.get("duration", 0),
                                "status": log_entry.get("status", "unknown"),
                                "result_summary": log_entry.get("result_summary", "")
                            })
                            
                    except json.JSONDecodeError:
                        continue
                        
            # Calculate average performance for each tool
            for tool_name, durations in stats["tool_performance"].items():
                if durations:
                    stats["tool_performance"][tool_name] = {
                        "average": sum(durations) / len(durations),
                        "min": min(durations),
                        "max": max(durations),
                        "count": len(durations)
                    }
                    
        except Exception as e:
            stats["error"] = f"Failed to analyze tool logs: {str(e)}"
            
        return stats
    
    def _analyze_api_logs(self) -> Dict[str, Any]:
        """Analyze API request logs."""
        log_file = self.log_dir / "api.json"
        
        stats = {
            "total_requests": 0,
            "status_codes": Counter(),
            "endpoints": Counter(),
            "methods": Counter(),
            "average_response_time": 0,
            "recent_requests": []
        }
        
        try:
            response_times = []
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        stats["total_requests"] += 1
                        
                        if log_entry.get("status_code"):
                            stats["status_codes"][log_entry["status_code"]] += 1
                            
                        if log_entry.get("path"):
                            stats["endpoints"][log_entry["path"]] += 1
                            
                        if log_entry.get("method"):
                            stats["methods"][log_entry["method"]] += 1
                            
                        if log_entry.get("duration"):
                            response_times.append(log_entry["duration"])
                            
                        # Add to recent requests
                        if len(stats["recent_requests"]) < 10:
                            stats["recent_requests"].append({
                                "timestamp": log_entry.get("timestamp"),
                                "method": log_entry.get("method"),
                                "path": log_entry.get("path"),
                                "status_code": log_entry.get("status_code"),
                                "duration": log_entry.get("duration", 0)
                            })
                            
                    except json.JSONDecodeError:
                        continue
                        
            if response_times:
                stats["average_response_time"] = sum(response_times) / len(response_times)
                
        except Exception as e:
            stats["error"] = f"Failed to analyze API logs: {str(e)}"
            
        return stats
    
    def _analyze_performance_logs(self) -> Dict[str, Any]:
        """Analyze performance logs."""
        log_file = self.log_dir / "performance.log"
        
        stats = {
            "operations": Counter(),
            "operation_times": defaultdict(list),
            "slow_operations": [],
            "total_operations": 0
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    match = re.search(r'Completed (.*?) in ([\d.]+)s', line)
                    if match:
                        operation, duration_str = match.groups()
                        duration = float(duration_str)
                        
                        stats["operations"][operation] += 1
                        stats["operation_times"][operation].append(duration)
                        stats["total_operations"] += 1
                        
                        # Track slow operations (> 5 seconds)
                        if duration > 5.0:
                            timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                            timestamp = timestamp_match.group(1) if timestamp_match else "unknown"
                            
                            stats["slow_operations"].append({
                                "timestamp": timestamp,
                                "operation": operation,
                                "duration": duration
                            })
                            
            # Calculate statistics for each operation
            for operation, times in stats["operation_times"].items():
                if times:
                    stats["operation_times"][operation] = {
                        "average": sum(times) / len(times),
                        "min": min(times),
                        "max": max(times),
                        "count": len(times)
                    }
                    
        except Exception as e:
            stats["error"] = f"Failed to analyze performance logs: {str(e)}"
            
        return stats
    
    def generate_report(self, output_file: Optional[str] = None) -> str:
        """
        Generate a comprehensive analysis report.
        
        Args:
            output_file: Optional file to save the report
            
        Returns:
            Report as string
        """
        analysis = self.analyze_all_logs()
        
        report_lines = [
            "=" * 80,
            "AGENTIC RAG SYSTEM - LOG ANALYSIS REPORT",
            "=" * 80,
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Log Directory: {self.log_dir}",
            "",
        ]
        
        # Summary section
        if analysis["summary"]:
            report_lines.extend([
                "SYSTEM SUMMARY",
                "-" * 40,
                f"Total log entries: {analysis['summary'].get('total_lines', 0)}",
                f"Log levels: {dict(analysis['summary'].get('log_levels', {}))}"
            ])
            
            if analysis["summary"].get("time_range"):
                report_lines.extend([
                    f"Time range: {analysis['summary']['time_range']['start']} to {analysis['summary']['time_range']['end']}"
                ])
            report_lines.append("")
        
        # Error analysis
        if analysis["errors"]:
            report_lines.extend([
                "ERROR ANALYSIS",
                "-" * 40,
                f"Total errors: {analysis['errors'].get('total_errors', 0)}",
                f"Error types: {dict(analysis['errors'].get('error_types', {}))}"
            ])
            
            if analysis["errors"].get("recent_errors"):
                report_lines.append("Recent errors:")
                for error in analysis["errors"]["recent_errors"][:5]:
                    report_lines.append(f"  - {error['timestamp']}: {error['message'][:100]}...")
            report_lines.append("")
        
        # Query analysis
        if analysis["queries"]:
            report_lines.extend([
                "QUERY ANALYSIS",
                "-" * 40,
                f"Total queries: {analysis['queries'].get('total_queries', 0)}",
                f"Successful: {analysis['queries'].get('successful_queries', 0)}",
                f"Failed: {analysis['queries'].get('failed_queries', 0)}",
                f"Average duration: {analysis['queries'].get('average_duration', 0):.3f}s",
                f"Bot usage: {dict(analysis['queries'].get('bots_usage', {}))}"
            ])
            report_lines.append("")
        
        # Tool analysis
        if analysis["tools"]:
            report_lines.extend([
                "TOOL ANALYSIS",
                "-" * 40,
                f"Total executions: {analysis['tools'].get('total_executions', 0)}",
                f"Successful: {analysis['tools'].get('successful_executions', 0)}",
                f"Failed: {analysis['tools'].get('failed_executions', 0)}",
                f"Tool usage: {dict(analysis['tools'].get('tool_usage', {}))}"
            ])
            
            if analysis["tools"].get("tool_performance"):
                report_lines.append("Tool performance:")
                for tool, perf in analysis["tools"]["tool_performance"].items():
                    if isinstance(perf, dict):
                        report_lines.append(f"  - {tool}: avg {perf['average']:.3f}s (min: {perf['min']:.3f}s, max: {perf['max']:.3f}s)")
            report_lines.append("")
        
        # API analysis
        if analysis["api"]:
            report_lines.extend([
                "API ANALYSIS",
                "-" * 40,
                f"Total requests: {analysis['api'].get('total_requests', 0)}",
                f"Average response time: {analysis['api'].get('average_response_time', 0):.3f}s",
                f"Status codes: {dict(analysis['api'].get('status_codes', {}))}"
            ])
            report_lines.append("")
        
        report_text = "\n".join(report_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
                
        return report_text


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Analyze Agentic RAG system logs")
    parser.add_argument("--log-dir", default="logs", help="Directory containing log files")
    parser.add_argument("--output", help="Output file for the report")
    parser.add_argument("--json", action="store_true", help="Output in JSON format")
    
    args = parser.parse_args()
    
    analyzer = LogAnalyzer(args.log_dir)
    
    if args.json:
        analysis = analyzer.analyze_all_logs()
        output = json.dumps(analysis, indent=2, default=str)
    else:
        output = analyzer.generate_report(args.output)
    
    if args.output:
        print(f"Report saved to: {args.output}")
    else:
        print(output)


if __name__ == "__main__":
    main()

"""
Centralized logging configuration for the Agentic RAG system.
Provides structured, file-based logging with rotation and different log levels.
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json


class CustomJSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_entry["extra"] = record.extra_data
            
        # Add request ID if present (for API requests)
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id
            
        # Add user info if present
        if hasattr(record, 'user_id'):
            log_entry["user_id"] = record.user_id
            
        # Add bot info if present
        if hasattr(record, 'bot_name'):
            log_entry["bot_name"] = record.bot_name
            
        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))


class ColoredConsoleFormatter(logging.Formatter):
    """Colored console formatter for better readability."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # Format the message
        formatted = super().format(record)
        
        # Add color
        return f"{color}{formatted}{reset}"


class LoggingConfig:
    """Centralized logging configuration manager."""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 log_level: str = "INFO",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 enable_console: bool = True,
                 enable_json_logs: bool = True):
        """
        Initialize logging configuration.
        
        Args:
            log_dir: Directory to store log files
            log_level: Default log level
            max_file_size: Maximum size of each log file in bytes
            backup_count: Number of backup files to keep
            enable_console: Whether to enable console logging
            enable_json_logs: Whether to enable JSON formatted logs
        """
        self.log_dir = Path(log_dir)
        self.log_level = getattr(logging, log_level.upper())
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_console = enable_console
        self.enable_json_logs = enable_json_logs
        
        # Create log directory
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        # Clear existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set root logger level
        root_logger.setLevel(self.log_level)
        
        # Setup different log files for different purposes
        self._setup_main_logger()
        self._setup_api_logger()
        self._setup_error_logger()
        self._setup_query_logger()
        self._setup_tool_logger()
        
        if self.enable_console:
            self._setup_console_logger()
    
    def _setup_main_logger(self) -> None:
        """Setup main application logger."""
        handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / "app.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        # Add to root logger
        logging.getLogger().addHandler(handler)
        
        # JSON logs if enabled
        if self.enable_json_logs:
            json_handler = logging.handlers.RotatingFileHandler(
                filename=self.log_dir / "app.json",
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            json_handler.setFormatter(CustomJSONFormatter())
            logging.getLogger().addHandler(json_handler)
    
    def _setup_api_logger(self) -> None:
        """Setup API request/response logger."""
        api_logger = logging.getLogger('api')
        api_logger.setLevel(logging.INFO)
        
        handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / "api.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - API - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        api_logger.addHandler(handler)
        
        # Prevent propagation to root logger to avoid duplication
        api_logger.propagate = False
        
        # JSON logs for API
        if self.enable_json_logs:
            json_handler = logging.handlers.RotatingFileHandler(
                filename=self.log_dir / "api.json",
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            json_handler.setFormatter(CustomJSONFormatter())
            api_logger.addHandler(json_handler)
    
    def _setup_error_logger(self) -> None:
        """Setup error-only logger."""
        error_logger = logging.getLogger('errors')
        error_logger.setLevel(logging.ERROR)
        
        handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / "errors.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - ERROR - %(name)s - %(message)s\n%(pathname)s:%(lineno)d in %(funcName)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        error_logger.addHandler(handler)
        error_logger.propagate = False
    
    def _setup_query_logger(self) -> None:
        """Setup query processing logger."""
        query_logger = logging.getLogger('queries')
        query_logger.setLevel(logging.INFO)
        
        handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / "queries.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - QUERY - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        query_logger.addHandler(handler)
        query_logger.propagate = False
    
    def _setup_tool_logger(self) -> None:
        """Setup tool execution logger."""
        tool_logger = logging.getLogger('tools')
        tool_logger.setLevel(logging.INFO)
        
        handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / "tools.log",
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - TOOL - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        tool_logger.addHandler(handler)
        tool_logger.propagate = False
    
    def _setup_console_logger(self) -> None:
        """Setup console logger with colors."""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        formatter = ColoredConsoleFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        # Add to root logger
        logging.getLogger().addHandler(console_handler)


# Global logging configuration instance
_logging_config: Optional[LoggingConfig] = None


def setup_logging(log_dir: str = "logs", 
                  log_level: str = None,
                  max_file_size: int = 10 * 1024 * 1024,
                  backup_count: int = 5,
                  enable_console: bool = True,
                  enable_json_logs: bool = True) -> LoggingConfig:
    """
    Setup global logging configuration.
    
    Args:
        log_dir: Directory to store log files
        log_level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        max_file_size: Maximum size of each log file in bytes
        backup_count: Number of backup files to keep
        enable_console: Whether to enable console logging
        enable_json_logs: Whether to enable JSON formatted logs
        
    Returns:
        LoggingConfig instance
    """
    global _logging_config
    
    # Get log level from environment if not specified
    if log_level is None:
        log_level = os.getenv('LOG_LEVEL', 'INFO')
    
    _logging_config = LoggingConfig(
        log_dir=log_dir,
        log_level=log_level,
        max_file_size=max_file_size,
        backup_count=backup_count,
        enable_console=enable_console,
        enable_json_logs=enable_json_logs
    )
    
    return _logging_config


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Specialized logger getters
def get_api_logger() -> logging.Logger:
    """Get API logger."""
    return logging.getLogger('api')


def get_error_logger() -> logging.Logger:
    """Get error logger."""
    return logging.getLogger('errors')


def get_query_logger() -> logging.Logger:
    """Get query logger."""
    return logging.getLogger('queries')


def get_tool_logger() -> logging.Logger:
    """Get tool logger."""
    return logging.getLogger('tools')

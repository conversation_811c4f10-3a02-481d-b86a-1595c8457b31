{"timestamp":"2025-07-04T09:27:22.556708","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:22.563678","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:22.570947","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:22.580271","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:27:22.584735","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:22.590183","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:22.738322","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:27:22.853660","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:27:22.853660","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:22.861761","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:22.892253","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:27:22.892851","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:22.893478","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:22.906430","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:22.909708","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:22.937232","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:22.937232","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:22.938342","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:22.938342","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:23.026236","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:23.026997","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:23.150419","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:32.080861","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:32.109097","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:32.110138","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:32.110138","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:32.110138","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:32.196348","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:32.196348","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:32.197347","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:32.209822","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:32.236133","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:32.236133","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:32.236920","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:32.236920","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:32.300467","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:32.300467","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:32.301477","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:32.353092","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:32.378152","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:27:40.017371","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:27:40.043460","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:40.062000","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:40.080272","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:40.119019","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:27:40.130239","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:40.137165","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:27:40.337204","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:27:40.422634","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:27:40.423162","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:40.432539","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:40.470979","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:27:40.470979","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:40.472468","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:40.491053","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:40.494360","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:40.526444","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:40.527107","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:40.527661","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:40.527661","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:40.598489","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:40.598489","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:40.714306","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:43.002311","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:43.026885","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:43.027391","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.028134","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:43.028768","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.088613","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:43.089638","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:43.093059","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:43.100849","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:43.137919","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:43.137919","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.138920","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:43.140426","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.209616","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:43.210123","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:43.210945","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.275851","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:43.318403","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:27:43.360088","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:27:43.360813","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.363847","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:43.388730","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:27:43.390252","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.391264","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:27:43.392263","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.392263","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:27:43.393262","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:27:43.398843","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:27:43.425043","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:27:43.426128","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:27:43.487696","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:27:43.488664","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:28:17.970019","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:28:17.980083","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:28:17.992279","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:28:18.000829","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:28:18.010286","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:28:18.013818","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:28:18.019832","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:28:18.183289","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:28:18.392420","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:28:18.393477","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:18.403526","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:18.437244","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:28:18.439775","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:18.441121","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:18.459455","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:28:18.461490","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:28:18.491318","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:28:18.491318","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:18.492315","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:28:18.493319","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:18.566038","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:28:18.566548","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:18.670757","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:28:32.987252","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:28:33.019337","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:28:33.021341","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.021341","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:28:33.022855","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.123321","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:28:33.124329","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:33.125872","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:28:33.143643","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:28:33.167666","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:28:33.168671","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.169677","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:28:33.169677","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.228140","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:28:33.229137","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:28:33.229137","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.284835","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:28:33.318001","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:28:33.365751","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:28:33.366929","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.370944","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:33.397253","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:28:33.397253","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.398255","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:28:33.398255","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.398255","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:28:33.400034","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:28:33.406974","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:28:33.433464","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:28:33.434541","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":140}
{"timestamp":"2025-07-04T09:28:33.493238","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":112}
{"timestamp":"2025-07-04T09:28:33.493238","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:29:09.820826","level":"INFO","logger":"app.main","message":"Processing query for bot: AdminBot","module":"main","function":"query_bot","line":171}
{"timestamp":"2025-07-04T09:29:11.664850","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:29:11.702564","level":"INFO","logger":"app.core.query_router","message":"Raw LLM response: ```json\n{\n  \"selected_tools\": [\"SQLQueryTool\"],\n  \"reasoning\": \"The query requests information about 'Identity.UserRoles', which appears to be a database table or entity. SQLQueryTool is appropriate to retrieve this data from a relational database.\"\n}\n```","module":"query_router","function":"_select_tools_with_reasoning","line":453}
{"timestamp":"2025-07-04T09:29:11.705731","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The query requests information about 'Identity.UserRoles', which appears to be a database table or entity. SQLQueryTool is appropriate to retrieve this data from a relational database.","module":"query_router","function":"_parse_llm_response","line":273}
{"timestamp":"2025-07-04T09:29:11.708934","level":"INFO","logger":"app.core.query_router","message":"Selected tools: ['SQLQueryTool']","module":"query_router","function":"route_query","line":106}
{"timestamp":"2025-07-04T09:29:11.712142","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The query requests information about 'Identity.UserRoles', which appears to be a database table or entity. SQLQueryTool is appropriate to retrieve this data from a relational database.","module":"query_router","function":"route_query","line":110}
{"timestamp":"2025-07-04T09:29:11.713331","level":"INFO","logger":"app.tools.sql_query","message":"Converting natural language to SQL query: Identity.UserRoles 'dan bilgi getir","module":"sql_query","function":"execute","line":171}
{"timestamp":"2025-07-04T09:29:13.043579","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:29:13.049196","level":"INFO","logger":"app.tools.sql_query","message":"LLM generated SQL query: SELECT * FROM Identity.UserRoles","module":"sql_query","function":"_convert_nl_to_sql_query","line":389}
{"timestamp":"2025-07-04T09:29:13.050690","level":"INFO","logger":"app.tools.sql_query","message":"LLM generated SQL query: SELECT * FROM Identity.UserRoles","module":"sql_query","function":"execute","line":176}
{"timestamp":"2025-07-04T09:29:13.391535","level":"ERROR","logger":"app.tools.sql_query","message":"Error executing SQL query: (pyodbc.ProgrammingError) ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Incorrect syntax near the keyword 'Identity'. (156) (SQLExecDirectW)\")\n[SQL: SELECT * FROM Identity.UserRoles]\n(Background on this error at: https://sqlalche.me/e/20/f405)","module":"sql_query","function":"execute","line":219}
{"timestamp":"2025-07-04T09:29:13.394289","level":"INFO","logger":"app.agents.langgraph_agent","message":"Processing query with tools: SQLQueryTool","module":"langgraph_agent","function":"process_query","line":373}
{"timestamp":"2025-07-04T09:29:13.396287","level":"INFO","logger":"app.agents.langgraph_agent","message":"SQLQueryTool result preview: {'success': False, 'error': '(pyodbc.ProgrammingError) (\\'42000\\', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Incorrect syntax near the keyword \\'Identity\\'. (156) (SQLExecDirectW)...","module":"langgraph_agent","function":"process_query","line":382}
{"timestamp":"2025-07-04T09:29:13.397858","level":"INFO","logger":"app.core.memory_manager","message":"Creating new memory for session: gradio-session-AdminBot","module":"memory_manager","function":"get_memory","line":36}
{"timestamp":"2025-07-04T09:29:13.398802","level":"INFO","logger":"app.agents.langgraph_agent","message":"Retrieved chat history for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":390}
{"timestamp":"2025-07-04T09:29:13.403243","level":"INFO","logger":"app.agents.langgraph_agent","message":"Formatting 1 context items for response generation","module":"langgraph_agent","function":"generate_response","line":237}
{"timestamp":"2025-07-04T09:29:13.405890","level":"INFO","logger":"app.agents.langgraph_agent","message":"Context sources used: SQL Database Error","module":"langgraph_agent","function":"generate_response","line":253}
{"timestamp":"2025-07-04T09:29:13.407570","level":"INFO","logger":"app.agents.langgraph_agent","message":"Context preview for response generation: Source: SQL Database Error\nContent: SQL query failed: (pyodbc.ProgrammingError) ('42000', \"[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Incorrect syntax near the keyword 'Identity'. (156) (SQLExecDirectW)\")\n[SQL: SELECT * FROM Identity.UserRoles]\n(Background on this error at: https:...","module":"langgraph_agent","function":"generate_response","line":266}
{"timestamp":"2025-07-04T09:29:17.855996","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:29:17.857100","level":"INFO","logger":"app.agents.langgraph_agent","message":"Successfully generated response using simple messages approach","module":"langgraph_agent","function":"generate_response","line":302}
{"timestamp":"2025-07-04T09:29:17.858014","level":"INFO","logger":"app.agents.langgraph_agent","message":"Generated response: \"Identity.UserRoles\" tablosuna erişim sırasında SQL Server'da \"Incorrect syntax near the keyword 'Identity'\" hatası alınmaktadır. Bunun sebebi, \"Identity\" ifadesinin SQL Server'da anahtar kelime olmas...","module":"langgraph_agent","function":"generate_response","line":323}
{"timestamp":"2025-07-04T09:29:17.858014","level":"INFO","logger":"app.agents.langgraph_agent","message":"Updated conversation memory for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":413}
{"timestamp":"2025-07-04T09:29:17.859004","level":"INFO","logger":"app.main","message":"Query processed successfully for bot: AdminBot, response length: 2104","module":"main","function":"query_bot","line":204}
{"timestamp":"2025-07-04T09:29:32.293454","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:29:32.310007","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:29:32.317282","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:29:32.327599","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:29:32.341476","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:29:32.348185","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:29:32.357077","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:29:32.495099","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:29:32.586398","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:29:32.587401","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:32.595330","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:32.623176","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:29:32.624370","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:32.625101","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:32.639896","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:29:32.643422","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:29:32.670151","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:29:32.671166","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:32.672080","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:29:32.672590","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:32.729855","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":113}
{"timestamp":"2025-07-04T09:29:32.730557","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:32.855239","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:29:34.311998","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:29:34.347853","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:29:34.348861","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.348861","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:29:34.349861","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.414235","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":113}
{"timestamp":"2025-07-04T09:29:34.415236","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:34.416245","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:29:34.424272","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:29:34.452064","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:29:34.452592","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.453105","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:29:34.453645","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.508207","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":113}
{"timestamp":"2025-07-04T09:29:34.509209","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:29:34.509209","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.561892","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":113}
{"timestamp":"2025-07-04T09:29:34.584340","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:29:34.628493","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:29:34.628493","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.631548","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:34.656113","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:29:34.657113","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.658114","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:29:34.658114","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.658114","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:29:34.659112","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:29:34.665317","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:29:34.689892","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:29:34.689892","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":141}
{"timestamp":"2025-07-04T09:29:34.751591","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":113}
{"timestamp":"2025-07-04T09:29:34.752097","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:30:20.937352","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:30:20.938535","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:30:20.938535","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:30:20.939209","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:30:20.952319","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:30:20.965304","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:30:20.976336","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:30:20.983704","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:30:20.988887","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:30:20.995124","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:30:20.996141","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.055s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:20.996141","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:30:20.996916","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:30:20.998055","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:30:21.195082","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:30:21.284262","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:30:21.285216","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:21.295520","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:21.327673","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:30:21.328288","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:21.329346","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:21.343168","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:30:21.345119","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:30:21.373371","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:30:21.373371","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:21.374924","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:30:21.374924","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:21.375969","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.378s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:21.375969","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:30:21.376938","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:21.377944","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:30:21.432559","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.054s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:21.531961","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.098s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:21.532976","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:30:21.533975","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:30:21.536694","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:21.662211","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:30:23.057673","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:30:23.075531","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:30:23.075531","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.075531","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:30:23.076536","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.076536","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.540s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.076536","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:30:23.077536","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.077536","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:30:23.096164","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.018s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.122390","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.026s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.123391","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:30:23.123391","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:30:23.123391","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:23.123391","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:30:23.129018","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:30:23.146412","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:30:23.147412","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.147412","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:30:23.148415","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.148415","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.025s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.148415","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:30:23.149413","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.149413","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:30:23.167260","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.018s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.195648","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.027s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.196658","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:30:23.196658","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:30:23.197385","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:30:23.197385","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.197385","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.198392","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:30:23.198392","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.199392","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:30:23.219741","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.020s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.255584","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.035s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.255584","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:30:23.255584","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:30:23.284951","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:30:23.332156","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:30:23.333155","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.336154","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:23.356484","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:30:23.356484","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.356484","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:30:23.357464","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.357464","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:30:23.358467","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:30:23.361981","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:30:23.382259","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:30:23.382259","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:30:23.383258","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.126s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.383258","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:30:23.384276","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.384276","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:30:23.404515","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.020s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.440276","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.035s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.440276","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:30:23.441282","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.445s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:30:23.441282","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:30:23.441282","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:31:26.892352","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:31:26.893351","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:31:26.893351","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:31:26.894351","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:31:26.901987","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:26.911173","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:26.921208","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:26.930188","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:31:26.932790","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:26.937941","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:26.939447","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.045s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:26.939447","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:31:26.940247","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:31:26.940247","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:27.097889","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:31:27.180224","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:27.180949","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:27.187254","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:27.207756","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:31:27.207756","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:27.207756","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:27.218250","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:31:27.220761","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:31:27.238137","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:31:27.238137","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:27.238137","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:27.239644","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:27.239644","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.299s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:27.240172","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:27.240172","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:27.240172","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:27.258564","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.017s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:27.289817","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.031s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:27.290339","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:27.290879","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:27.291429","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:27.376854","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:31:28.508445","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:31:28.526030","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:31:28.527034","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.527034","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:28.528030","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.528030","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.237s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.528030","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:28.529032","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.529032","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:28.548603","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.020s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.577297","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.029s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.577297","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:28.578563","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:28.578563","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:28.578563","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:31:28.585303","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:31:28.605179","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:31:28.605179","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.606177","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:28.606177","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.607175","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.029s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.607175","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:28.607175","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.607175","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:28.627102","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.018s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.657394","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.029s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.657394","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:28.658339","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:28.659340","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:28.659340","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.660339","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.660856","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:28.661859","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.661859","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:28.683927","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.021s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.711299","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.026s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.712298","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:28.712298","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:28.732427","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:31:28.763861","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:28.765872","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.768905","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:28.792076","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:31:28.793365","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.793365","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:28.794384","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.794384","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:28.795721","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":44}
{"timestamp":"2025-07-04T09:31:28.798949","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":278}
{"timestamp":"2025-07-04T09:31:28.831481","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":58}
{"timestamp":"2025-07-04T09:31:28.831481","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:28.832964","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.121s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.832964","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:28.834155","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.834155","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:28.870870","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.035s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.938274","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.066s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.939429","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:28.939966","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:28.939966","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:31:28.940575","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:31:43.338640","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:31:43.339357","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:31:43.339357","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:31:43.339870","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:31:43.346038","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:43.350006","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:43.354741","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:43.360145","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:31:43.363039","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:43.367566","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:31:43.367566","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.027s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:43.367566","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:31:43.368605","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:31:43.368605","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:43.465358","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:31:43.542707","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:43.543346","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:43.550368","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:43.574026","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:31:43.575547","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:43.575547","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:43.587072","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:31:43.589752","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":279}
{"timestamp":"2025-07-04T09:31:43.610344","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:31:43.610344","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:43.611351","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:43.612796","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:43.612796","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.244s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:43.612796","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:43.613969","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:43.613969","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:43.714807","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.100s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:43.809921","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.092s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:43.811047","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:43.813053","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:43.814245","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:43.967596","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:31:45.349131","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":279}
{"timestamp":"2025-07-04T09:31:45.371319","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:31:45.372824","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.372824","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:45.373833","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.373833","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.560s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.373833","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:45.375340","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.002s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.375340","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:45.403231","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.028s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.437580","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.033s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.438930","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:45.438930","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:45.439937","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:45.440457","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:31:45.447021","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":279}
{"timestamp":"2025-07-04T09:31:45.472060","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:31:45.472060","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.472060","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:45.473521","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.473521","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.034s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.473521","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:45.475040","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.475568","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:45.496156","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.021s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.542206","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.046s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.543205","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:45.543205","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:45.544207","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:45.545204","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.545204","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.546469","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:45.547478","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.548481","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:45.596825","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.048s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.654280","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.056s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.655292","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:45.656290","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:31:45.696939","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:31:45.758109","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:45.759111","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.763163","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":39}
{"timestamp":"2025-07-04T09:31:45.795980","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":50}
{"timestamp":"2025-07-04T09:31:45.796982","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.796982","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:31:45.797982","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.797982","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:31:45.798982","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:31:45.805093","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":279}
{"timestamp":"2025-07-04T09:31:45.835371","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:31:45.835371","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:31:45.836367","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.178s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.837366","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:31:45.838367","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.838367","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:31:45.870408","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.031s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.991095","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.120s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.992096","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:31:45.993110","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.626s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:31:45.994106","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:31:45.998701","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:32:48.997741","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:32:48.997741","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:32:48.998746","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:32:48.998746","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:32:49.005454","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:32:49.010455","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:32:49.017844","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:32:49.025578","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:32:49.028528","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:32:49.034224","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:32:49.034224","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.035s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:49.035230","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:32:49.035230","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:32:49.036226","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:32:49.120955","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:32:49.183460","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:32:49.184168","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:49.190525","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:49.210711","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:32:49.210711","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:49.211811","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:49.221428","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:32:49.223021","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:32:49.240485","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:32:49.240485","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:49.241588","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:32:49.241588","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:49.241588","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.205s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:49.242581","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:32:49.242581","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:49.243590","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:32:49.260700","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.017s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:49.290329","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.029s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:49.290934","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:32:49.290934","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:32:49.290934","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:49.366094","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:32:50.634796","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:32:50.676343","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:32:50.677345","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:50.678344","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:32:50.678344","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:50.679344","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.388s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.679344","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:32:50.681361","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.681361","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:32:50.725252","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.043s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.793190","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.067s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.794366","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:32:50.794913","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:32:50.795448","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:50.796597","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:32:50.809560","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:32:50.848618","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:32:50.851151","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:50.854364","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:32:50.856887","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:50.858360","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.063s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.858360","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:32:50.859373","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.860373","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:32:50.907339","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.046s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.982468","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.074s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.985522","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:32:50.986523","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:32:50.988209","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:32:50.991278","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:50.992278","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.004s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.993278","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:32:50.995280","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:50.996279","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:32:51.053308","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.057s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.121599","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.067s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.121599","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:32:51.122604","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:32:51.153598","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:32:51.212131","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:32:51.213322","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:51.217873","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:51.250384","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:32:51.250969","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:51.251980","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":33}
{"timestamp":"2025-07-04T09:32:51.251980","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:51.251980","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:32:51.253991","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:32:51.259427","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:32:51.289940","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:32:51.290448","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:32:51.291460","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.168s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.292458","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:32:51.292458","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.293458","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:32:51.319230","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.025s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.362098","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.042s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.362098","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:32:51.363317","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.328s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:32:51.364328","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:32:51.364328","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:36:15.406906","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:36:15.406906","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:36:15.407903","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:36:15.407903","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:36:15.416614","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:36:15.422048","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:36:15.427434","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:36:15.435931","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:36:15.442536","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\logging_config.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'logging': {'log_dir': '...schedule': '0 2 * * *'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'logging': {'log_dir': '...schedule': '0 2 * * *'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:36:15.446073","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:36:15.451149","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:36:15.451149","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.042s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:15.452150","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:36:15.452150","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:36:15.453148","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:36:15.548259","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:36:15.638218","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:36:15.639583","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:15.652447","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:15.683971","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:36:15.685477","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:15.686501","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:15.702904","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:36:15.705093","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:36:15.728853","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:36:15.729386","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:15.730507","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:36:15.730507","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:15.731041","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.278s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:15.731606","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:36:15.732189","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:15.732731","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:36:15.751298","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.019s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:15.776747","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.025s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:15.776747","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:36:15.776747","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:36:15.776747","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:15.857685","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:36:17.003351","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:36:17.036547","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:36:17.037556","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.038556","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:36:17.038556","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.039556","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.263s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.039556","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:36:17.040681","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.040681","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:36:17.079234","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.037s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.137935","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.058s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.138470","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:36:17.139109","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:36:17.139642","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:17.141172","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:36:17.158814","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:36:17.191760","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:36:17.194634","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.195645","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:36:17.195645","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.196648","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.057s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.196648","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:36:17.197649","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.198643","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:36:17.224797","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.026s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.264761","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.039s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.265759","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:36:17.265759","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:36:17.265759","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:36:17.267182","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.268192","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.002s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.269199","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:36:17.269199","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.270412","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:36:17.299213","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.028s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.348391","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.049s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.349394","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:36:17.350747","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:36:17.381068","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:36:17.442157","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:36:17.444167","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.449214","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:17.481822","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:36:17.482802","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.482802","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:36:17.482802","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.484940","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:36:17.485951","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:36:17.492925","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:36:17.524365","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:36:17.524871","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:36:17.525884","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.174s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.526882","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:36:17.528884","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.528884","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:36:17.557311","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.027s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.596192","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.038s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.597193","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:36:17.598181","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.146s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:36:17.598181","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:36:17.599180","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:40:32.389910","level":"INFO","logger":"app.main","message":"Initializing AgenticRAG system...","module":"main","function":"<module>","line":42}
{"timestamp":"2025-07-04T09:40:32.391162","level":"INFO","logger":"app.core.agentic_rag","message":"Initializing AgenticRAG system...","module":"agentic_rag","function":"__init__","line":78}
{"timestamp":"2025-07-04T09:40:32.391162","level":"INFO","logger":"app.core.agentic_rag","message":"Config directory: configs","module":"agentic_rag","function":"__init__","line":83}
{"timestamp":"2025-07-04T09:40:32.392065","level":"INFO","logger":"app.core.agentic_rag","message":"Prompts directory: prompts","module":"agentic_rag","function":"__init__","line":84}
{"timestamp":"2025-07-04T09:40:32.400594","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AcademicBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:40:32.406224","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AdminBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:40:32.412243","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: AtlasIQBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:40:32.417835","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\document_processing.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'default_settings': {'da...progress_interval': 10}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:40:32.426170","level":"ERROR","logger":"app.core.config_loader","message":"Error loading config from configs\\logging_config.yaml: 2 validation errors for BotConfig\nname\n  Field required [type=missing, input_value={'logging': {'log_dir': '...schedule': '0 2 * * *'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nprompts\n  Field required [type=missing, input_value={'logging': {'log_dir': '...schedule': '0 2 * * *'}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing","module":"config_loader","function":"_load_configs","line":45}
{"timestamp":"2025-07-04T09:40:32.428858","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: SimpleBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:40:32.434179","level":"INFO","logger":"app.core.config_loader","message":"Loaded configuration for bot: StudentBot","module":"config_loader","function":"_load_configs","line":43}
{"timestamp":"2025-07-04T09:40:32.434179","level":"INFO","logger":"performance","message":"Completed Config loader initialization in 0.042s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:32.434179","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot configurations...","module":"agentic_rag","function":"_load_bots","line":100}
{"timestamp":"2025-07-04T09:40:32.435533","level":"INFO","logger":"app.core.agentic_rag","message":"Found 5 bot configurations","module":"agentic_rag","function":"_load_bots","line":102}
{"timestamp":"2025-07-04T09:40:32.435533","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:40:32.534088","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:40:32.633992","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: academic_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:40:32.633992","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:32.642635","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: academic_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:32.666443","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:40:32.667444","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:32.667444","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:32.682947","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:40:32.684463","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 0 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:40:32.708378","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:40:32.709276","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:32.709276","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:40:32.709276","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:32.710272","level":"INFO","logger":"performance","message":"Completed Tool initialization for AcademicBot in 0.275s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:32.710943","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:40:32.711956","level":"INFO","logger":"performance","message":"Completed Prompt loading for AcademicBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:32.712802","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:40:32.734616","level":"INFO","logger":"performance","message":"Completed Query router initialization for AcademicBot in 0.021s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:32.764767","level":"INFO","logger":"performance","message":"Completed Agent initialization for AcademicBot in 0.030s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:32.765767","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AcademicBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:40:32.765767","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:40:32.765767","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: mssql","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:32.857169","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:40:34.358118","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 3 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:40:34.381855","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:40:34.381855","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.383262","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:40:34.383262","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.383262","level":"INFO","logger":"performance","message":"Completed Tool initialization for AdminBot in 1.617s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.384362","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:40:34.384965","level":"INFO","logger":"performance","message":"Completed Prompt loading for AdminBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.384965","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:40:34.408653","level":"INFO","logger":"performance","message":"Completed Query router initialization for AdminBot in 0.023s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.440382","level":"INFO","logger":"performance","message":"Completed Agent initialization for AdminBot in 0.031s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.441479","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AdminBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:40:34.442483","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:40:34.442483","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:34.442483","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\atlasiq_test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:40:34.450904","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:40:34.472339","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:40:34.472339","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.472339","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:40:34.473943","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.473943","level":"INFO","logger":"performance","message":"Completed Tool initialization for AtlasIQBot in 0.031s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.473943","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 2 tools for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:40:34.473943","level":"INFO","logger":"performance","message":"Completed Prompt loading for AtlasIQBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.475580","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:40:34.496184","level":"INFO","logger":"performance","message":"Completed Query router initialization for AtlasIQBot in 0.019s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.525283","level":"INFO","logger":"performance","message":"Completed Agent initialization for AtlasIQBot in 0.029s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.525890","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: AtlasIQBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:40:34.525890","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:40:34.526994","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:40:34.528520","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.528520","level":"INFO","logger":"performance","message":"Completed Tool initialization for SimpleBot in 0.002s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.528520","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 1 tools for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:40:34.529769","level":"INFO","logger":"performance","message":"Completed Prompt loading for SimpleBot in 0.001s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.529769","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:40:34.550938","level":"INFO","logger":"performance","message":"Completed Query router initialization for SimpleBot in 0.021s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.578231","level":"INFO","logger":"performance","message":"Completed Agent initialization for SimpleBot in 0.027s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.578231","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: SimpleBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:40:34.579473","level":"INFO","logger":"app.core.agentic_rag","message":"Loading bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":106}
{"timestamp":"2025-07-04T09:40:34.600234","level":"INFO","logger":"chromadb.telemetry.product.posthog","message":"Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.","module":"posthog","function":"__init__","line":22}
{"timestamp":"2025-07-04T09:40:34.643077","level":"INFO","logger":"app.tools.document_search","message":"Initialized document search tool with collection: student_documents","module":"document_search","function":"initialize","line":41}
{"timestamp":"2025-07-04T09:40:34.644077","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: DocumentSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.647867","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized MongoDB query tool for database: vector_db","module":"mongodb_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:34.677329","level":"INFO","logger":"app.tools.mongodb_query","message":"Initialized LLM for MongoDB query conversion: gpt-4.1-mini","module":"mongodb_query","function":"initialize","line":51}
{"timestamp":"2025-07-04T09:40:34.677329","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: MongoDBQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.677329","level":"INFO","logger":"app.tools.web_search","message":"Initialized web search tool with TavilySearch API","module":"web_search","function":"initialize","line":37}
{"timestamp":"2025-07-04T09:40:34.678720","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: WebSearchTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.679352","level":"INFO","logger":"app.tools.sql_query","message":"Detected database type: sqlite","module":"sql_query","function":"initialize","line":40}
{"timestamp":"2025-07-04T09:40:34.679352","level":"INFO","logger":"app.tools.sql_query","message":"Initialized SQL query tool with connection: sqlite:///C:\\Users\\<USER>\\Desktop\\WORK\\Agentic RAG\\tests\\test_db.sqlite","module":"sql_query","function":"initialize","line":45}
{"timestamp":"2025-07-04T09:40:34.685318","level":"INFO","logger":"app.tools.sql_query","message":"Retrieved schema information for 4 tables","module":"sql_query","function":"_get_table_schemas","line":280}
{"timestamp":"2025-07-04T09:40:34.708325","level":"INFO","logger":"app.tools.sql_query","message":"Initialized LLM for SQL query conversion: gpt-4.1-mini","module":"sql_query","function":"initialize","line":59}
{"timestamp":"2025-07-04T09:40:34.708325","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized tool: SQLQueryTool","module":"agentic_rag","function":"_initialize_tools","line":174}
{"timestamp":"2025-07-04T09:40:34.709332","level":"INFO","logger":"performance","message":"Completed Tool initialization for StudentBot in 0.130s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.709332","level":"INFO","logger":"app.core.agentic_rag","message":"Initialized 4 tools for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":111}
{"timestamp":"2025-07-04T09:40:34.710544","level":"INFO","logger":"performance","message":"Completed Prompt loading for StudentBot in 0.000s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.710544","level":"INFO","logger":"app.core.agentic_rag","message":"Loaded prompts for bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":121}
{"timestamp":"2025-07-04T09:40:34.732794","level":"INFO","logger":"performance","message":"Completed Query router initialization for StudentBot in 0.021s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.764172","level":"INFO","logger":"performance","message":"Completed Agent initialization for StudentBot in 0.031s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.764172","level":"INFO","logger":"app.core.agentic_rag","message":"Successfully loaded bot: StudentBot","module":"agentic_rag","function":"_load_bots","line":141}
{"timestamp":"2025-07-04T09:40:34.765519","level":"INFO","logger":"performance","message":"Completed Bot configurations loading in 2.331s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:40:34.765519","level":"INFO","logger":"app.core.agentic_rag","message":"AgenticRAG system initialized with 5 bots","module":"agentic_rag","function":"__init__","line":96}
{"timestamp":"2025-07-04T09:40:34.765519","level":"INFO","logger":"app.main","message":"AgenticRAG system initialized successfully","module":"main","function":"<module>","line":44}
{"timestamp":"2025-07-04T09:41:32.950739","level":"INFO","logger":"app.main","message":"Processing query for bot: AdminBot","module":"main","function":"query_bot","line":171}
{"timestamp":"2025-07-04T09:41:32.951376","level":"INFO","logger":"app.core.agentic_rag","message":"Processing query for bot: AdminBot","module":"agentic_rag","function":"process_query","line":261}
{"timestamp":"2025-07-04T09:41:32.952711","level":"INFO","logger":"app.core.agentic_rag","message":"Routing query to tools for bot: AdminBot","module":"agentic_rag","function":"process_query","line":274}
{"timestamp":"2025-07-04T09:41:35.266958","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:41:35.299401","level":"INFO","logger":"app.core.query_router","message":"Raw LLM response: ```json\n{\n  \"selected_tools\": [],\n  \"reasoning\": \"The query is a simple greeting in Turkish ('Merhaba' means 'Hello'). It does not require any external data or tools to respond.\"\n}\n```","module":"query_router","function":"_select_tools_with_reasoning","line":453}
{"timestamp":"2025-07-04T09:41:35.300623","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The query is a simple greeting in Turkish ('Merhaba' means 'Hello'). It does not require any external data or tools to respond.","module":"query_router","function":"_parse_llm_response","line":273}
{"timestamp":"2025-07-04T09:41:35.301270","level":"INFO","logger":"app.core.query_router","message":"LLM explicitly decided to use no tools for this query","module":"query_router","function":"_parse_llm_response","line":282}
{"timestamp":"2025-07-04T09:41:35.302601","level":"INFO","logger":"app.core.query_router","message":"Selected tools: []","module":"query_router","function":"route_query","line":106}
{"timestamp":"2025-07-04T09:41:35.302601","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The query is a simple greeting in Turkish ('Merhaba' means 'Hello'). It does not require any external data or tools to respond.","module":"query_router","function":"route_query","line":110}
{"timestamp":"2025-07-04T09:41:35.303619","level":"INFO","logger":"performance","message":"Completed Tool routing for AdminBot in 2.350s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:41:35.309253","level":"INFO","logger":"app.core.agentic_rag","message":"Selected tools for bot AdminBot: ","module":"agentic_rag","function":"process_query","line":287}
{"timestamp":"2025-07-04T09:41:35.310268","level":"INFO","logger":"app.core.agentic_rag","message":"Processing query with agent for bot: AdminBot","module":"agentic_rag","function":"process_query","line":292}
{"timestamp":"2025-07-04T09:41:35.518727","level":"INFO","logger":"app.agents.langgraph_agent","message":"Processing query without any tool results","module":"langgraph_agent","function":"process_query","line":384}
{"timestamp":"2025-07-04T09:41:35.519987","level":"INFO","logger":"app.core.memory_manager","message":"Creating new memory for session: gradio-session-AdminBot","module":"memory_manager","function":"get_memory","line":36}
{"timestamp":"2025-07-04T09:41:35.533098","level":"INFO","logger":"app.agents.langgraph_agent","message":"Retrieved chat history for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":390}
{"timestamp":"2025-07-04T09:41:35.540637","level":"INFO","logger":"app.agents.langgraph_agent","message":"Formatting 0 context items for response generation","module":"langgraph_agent","function":"generate_response","line":237}
{"timestamp":"2025-07-04T09:41:35.541647","level":"INFO","logger":"app.agents.langgraph_agent","message":"Context preview for response generation: ","module":"langgraph_agent","function":"generate_response","line":266}
{"timestamp":"2025-07-04T09:41:37.745555","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:41:37.753091","level":"INFO","logger":"app.agents.langgraph_agent","message":"Successfully generated response using simple messages approach","module":"langgraph_agent","function":"generate_response","line":302}
{"timestamp":"2025-07-04T09:41:37.755090","level":"INFO","logger":"app.agents.langgraph_agent","message":"Generated response: Merhaba! Size nasıl yardımcı olabilirim?","module":"langgraph_agent","function":"generate_response","line":323}
{"timestamp":"2025-07-04T09:41:37.757090","level":"INFO","logger":"app.agents.langgraph_agent","message":"Updated conversation memory for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":413}
{"timestamp":"2025-07-04T09:41:37.757597","level":"INFO","logger":"performance","message":"Completed Agent processing for AdminBot in 2.240s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:41:37.758611","level":"INFO","logger":"app.core.agentic_rag","message":"Agent processing completed for bot: AdminBot","module":"agentic_rag","function":"process_query","line":302}
{"timestamp":"2025-07-04T09:41:37.758611","level":"INFO","logger":"app.core.agentic_rag","message":"Query processed successfully for bot: AdminBot, response length: 40","module":"agentic_rag","function":"process_query","line":340}
{"timestamp":"2025-07-04T09:41:37.760907","level":"INFO","logger":"app.main","message":"Query processed successfully for bot: AdminBot, response length: 817","module":"main","function":"query_bot","line":204}
{"timestamp":"2025-07-04T09:42:15.006974","level":"INFO","logger":"app.main","message":"Processing query for bot: AdminBot","module":"main","function":"query_bot","line":171}
{"timestamp":"2025-07-04T09:42:15.008038","level":"INFO","logger":"app.core.agentic_rag","message":"Processing query for bot: AdminBot","module":"agentic_rag","function":"process_query","line":261}
{"timestamp":"2025-07-04T09:42:15.009046","level":"INFO","logger":"app.core.agentic_rag","message":"Routing query to tools for bot: AdminBot","module":"agentic_rag","function":"process_query","line":274}
{"timestamp":"2025-07-04T09:42:16.661521","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:42:16.671296","level":"INFO","logger":"app.core.query_router","message":"Raw LLM response: ```json\n{\n  \"selected_tools\": [\"WebSearchTool\"],\n  \"reasoning\": \"The user is asking to find a YouTube link for a Python course. The WebSearchTool can search the web, including YouTube, to find relevant links.\"\n}\n```","module":"query_router","function":"_select_tools_with_reasoning","line":453}
{"timestamp":"2025-07-04T09:42:16.672306","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The user is asking to find a YouTube link for a Python course. The WebSearchTool can search the web, including YouTube, to find relevant links.","module":"query_router","function":"_parse_llm_response","line":273}
{"timestamp":"2025-07-04T09:42:16.673755","level":"INFO","logger":"app.core.query_router","message":"Selected tools: ['WebSearchTool']","module":"query_router","function":"route_query","line":106}
{"timestamp":"2025-07-04T09:42:16.674270","level":"INFO","logger":"app.core.query_router","message":"Tool selection reasoning: The user is asking to find a YouTube link for a Python course. The WebSearchTool can search the web, including YouTube, to find relevant links.","module":"query_router","function":"route_query","line":110}
{"timestamp":"2025-07-04T09:42:17.247050","level":"ERROR","logger":"app.tools.web_search","message":"Error executing web search: Invalid search depth. Must be 'basic' or 'advanced'.","module":"web_search","function":"execute","line":87}
{"timestamp":"2025-07-04T09:42:17.248669","level":"INFO","logger":"performance","message":"Completed Tool routing for AdminBot in 2.239s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:42:17.249812","level":"INFO","logger":"app.core.agentic_rag","message":"Selected tools for bot AdminBot: WebSearchTool","module":"agentic_rag","function":"process_query","line":287}
{"timestamp":"2025-07-04T09:42:17.250322","level":"INFO","logger":"app.core.agentic_rag","message":"Processing query with agent for bot: AdminBot","module":"agentic_rag","function":"process_query","line":292}
{"timestamp":"2025-07-04T09:42:17.250895","level":"INFO","logger":"app.agents.langgraph_agent","message":"Processing query with tools: WebSearchTool","module":"langgraph_agent","function":"process_query","line":373}
{"timestamp":"2025-07-04T09:42:17.251611","level":"INFO","logger":"app.agents.langgraph_agent","message":"WebSearchTool result preview: {'success': False, 'error': \"Invalid search depth. Must be 'basic' or 'advanced'.\", 'results': []}","module":"langgraph_agent","function":"process_query","line":382}
{"timestamp":"2025-07-04T09:42:17.252623","level":"INFO","logger":"app.agents.langgraph_agent","message":"Retrieved chat history for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":390}
{"timestamp":"2025-07-04T09:42:17.254970","level":"INFO","logger":"app.agents.langgraph_agent","message":"Formatting 0 context items for response generation","module":"langgraph_agent","function":"generate_response","line":237}
{"timestamp":"2025-07-04T09:42:17.255505","level":"INFO","logger":"app.agents.langgraph_agent","message":"Context preview for response generation: ","module":"langgraph_agent","function":"generate_response","line":266}
{"timestamp":"2025-07-04T09:42:17.257118","level":"INFO","logger":"app.agents.langgraph_agent","message":"Including chat history in prompt (length: 60)","module":"langgraph_agent","function":"generate_response","line":279}
{"timestamp":"2025-07-04T09:42:22.361363","level":"INFO","logger":"httpx","message":"HTTP Request: POST https://api.openai.com/v1/chat/completions \"HTTP/1.1 200 OK\"","module":"_client","function":"_send_single_request","line":1025}
{"timestamp":"2025-07-04T09:42:22.363365","level":"INFO","logger":"app.agents.langgraph_agent","message":"Successfully generated response using simple messages approach","module":"langgraph_agent","function":"generate_response","line":302}
{"timestamp":"2025-07-04T09:42:22.363365","level":"INFO","logger":"app.agents.langgraph_agent","message":"Generated response: Python kursu arıyorsanız, YouTube üzerinde birçok ücretsiz ve kaliteli eğitim bulunmaktadır. İşte popüler ve kapsamlı Python kurslarından bazıları ile linkleri:\n\n1. **Python Programlama Dili - Temel v...","module":"langgraph_agent","function":"generate_response","line":323}
{"timestamp":"2025-07-04T09:42:22.364853","level":"INFO","logger":"app.agents.langgraph_agent","message":"Updated conversation memory for session gradio-session-AdminBot","module":"langgraph_agent","function":"process_query","line":413}
{"timestamp":"2025-07-04T09:42:22.364853","level":"INFO","logger":"performance","message":"Completed Agent processing for AdminBot in 5.114s","module":"logging_helpers","function":"__exit__","line":375}
{"timestamp":"2025-07-04T09:42:22.364853","level":"INFO","logger":"app.core.agentic_rag","message":"Agent processing completed for bot: AdminBot","module":"agentic_rag","function":"process_query","line":302}
{"timestamp":"2025-07-04T09:42:22.365860","level":"INFO","logger":"app.core.agentic_rag","message":"Query processed successfully for bot: AdminBot, response length: 820","module":"agentic_rag","function":"process_query","line":340}
{"timestamp":"2025-07-04T09:42:22.368382","level":"INFO","logger":"app.main","message":"Query processed successfully for bot: AdminBot, response length: 1883","module":"main","function":"query_bot","line":204}

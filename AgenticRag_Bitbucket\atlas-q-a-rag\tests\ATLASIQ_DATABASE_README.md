# AtlasIQ Test Database

Bu dosya, AtlasIQBot için oluşturulan test veritabanı hakkında bilgi içerir.

## Veritabanı Bilgileri

- **Do<PERSON>a Adı**: `atlasiq_test_db.sqlite`
- **Konum**: `tests/atlasiq_test_db.sqlite`
- **Tür**: SQLite3 veritabanı
- **<PERSON><PERSON><PERSON>**: UTF-8 (Türkçe karakterler desteklenir)

## Tablo Yapısı

### 1. institutions (Kurumlar)
Türkiye'deki üniversiteler ve araştırma kurumları

| Sütun | Tür | Açıklama |
|-------|-----|----------|
| id | INTEGER PRIMARY KEY | Kurum ID |
| name | TEXT NOT NULL | Kurum adı |
| type | TEXT NOT NULL | Kurum türü (Public University, Private University, Research Institution) |
| city | TEXT | Şehir |
| country | TEXT | Ülke |
| established_year | INTEGER | Kuruluş yılı |
| website | TEXT | Web sitesi |
| contact_email | TEXT | İletişim e-postası |

**Örnek Veriler**: Atlas Üniversitesi, İTÜ, Boğaziçi, ODTÜ, Sabancı, TÜBİTAK, Koç, Bilkent

### 2. researchers (Araştırmacılar)
Akademisyenler ve araştırmacılar

| Sütun | Tür | Açıklama |
|-------|-----|----------|
| id | INTEGER PRIMARY KEY | Araştırmacı ID |
| name | TEXT NOT NULL | Ad soyad |
| title | TEXT | Akademik unvan |
| institution_id | INTEGER | Kurum ID (FK) |
| department | TEXT | Bölüm |
| research_areas | TEXT | Araştırma alanları |
| email | TEXT | E-posta |
| orcid | TEXT | ORCID ID |
| h_index | INTEGER | H-indeks |

**Örnek Veriler**: Prof. Dr. Mehmet Özkan (AI), Doç. Dr. Ayşe Yılmaz (NLP), vb.

### 3. grants (Hibeler)
Araştırma hibeleri ve projeleri

| Sütun | Tür | Açıklama |
|-------|-----|----------|
| id | INTEGER PRIMARY KEY | Hibe ID |
| title | TEXT NOT NULL | Proje başlığı |
| funding_agency | TEXT | Fon sağlayan kurum |
| principal_investigator_id | INTEGER | Proje yürütücüsü ID (FK) |
| institution_id | INTEGER | Kurum ID (FK) |
| amount | REAL | Hibe miktarı |
| currency | TEXT | Para birimi |
| start_date | TEXT | Başlangıç tarihi |
| end_date | TEXT | Bitiş tarihi |
| status | TEXT | Durum |
| research_area | TEXT | Araştırma alanı |

**Örnek Veriler**: TÜBİTAK, TÜBA, AB Horizon, NATO SPS hibeleri

### 4. publications (Yayınlar)
Akademik yayınlar

| Sütun | Tür | Açıklama |
|-------|-----|----------|
| id | INTEGER PRIMARY KEY | Yayın ID |
| title | TEXT NOT NULL | Makale başlığı |
| authors | TEXT | Yazarlar |
| journal | TEXT | Dergi adı |
| publication_year | INTEGER | Yayın yılı |
| volume | TEXT | Cilt |
| issue | TEXT | Sayı |
| pages | TEXT | Sayfa numaraları |
| doi | TEXT | DOI |
| citation_count | INTEGER | Atıf sayısı |
| research_area | TEXT | Araştırma alanı |
| institution_id | INTEGER | Kurum ID (FK) |
| grant_id | INTEGER | Hibe ID (FK) |

**Örnek Veriler**: AI, NLP, Computer Vision, Cybersecurity alanlarında yayınlar

## Örnek Sorgular

### Temel Sorgular
```sql
-- Tüm kurumları listele
SELECT name, city, type FROM institutions WHERE country = 'Türkiye';

-- Atlas Üniversitesi araştırmacıları
SELECT name, title, research_areas FROM researchers r 
JOIN institutions i ON r.institution_id = i.id 
WHERE i.name = 'Atlas Üniversitesi';

-- Aktif hibeler
SELECT title, funding_agency, amount, currency 
FROM grants WHERE status = 'Active';

-- Son yayınlar
SELECT title, authors, journal, publication_year 
FROM publications WHERE publication_year >= 2022 
ORDER BY publication_year DESC;
```

### Gelişmiş Sorgular
```sql
-- AI araştırması yapan kişiler ve hibeleri
SELECT r.name, i.name as institution, g.title 
FROM researchers r 
JOIN institutions i ON r.institution_id = i.id 
JOIN grants g ON g.principal_investigator_id = r.id 
WHERE g.research_area LIKE '%Artificial Intelligence%';

-- Türkçe ile ilgili araştırmalar
SELECT title, authors, journal 
FROM publications 
WHERE title LIKE '%Turkish%' OR title LIKE '%Türkçe%';

-- En çok atıf alan yayınlar
SELECT title, authors, citation_count, journal 
FROM publications 
ORDER BY citation_count DESC LIMIT 5;
```

## Kullanım

1. **Veritabanını oluşturmak için**:
   ```bash
   cd tests
   python create_atlasiq_db.py
   ```

2. **Veritabanını test etmek için**:
   ```bash
   cd tests
   python test_atlasiq_db.py
   ```

3. **AtlasIQBot ile kullanım**:
   - `configs/atlasiq_bot.yaml` dosyasında veritabanı yolu otomatik olarak güncellendi
   - Bot artık bu akademik veritabanını kullanarak sorulara yanıt verebilir

## Özellikler

- ✅ Türkçe karakter desteği
- ✅ Gerçekçi akademik veriler
- ✅ İlişkisel tablo yapısı (Foreign Keys)
- ✅ Çeşitli araştırma alanları
- ✅ Türkiye odaklı kurumlar
- ✅ Test edilmiş ve doğrulanmış

## Veri İçeriği

- **8 kurum** (Atlas Üniversitesi, İTÜ, Boğaziçi, ODTÜ, vb.)
- **10 araştırmacı** (Profesör, Doçent, Dr. Öğr. Üyesi, Arş. Gör.)
- **10 hibe** (TÜBİTAK, TÜBA, AB, NATO hibeleri)
- **10 yayın** (AI, NLP, Computer Vision, Cybersecurity alanlarında)

Bu veritabanı AtlasIQBot'un SQL sorgulama yeteneklerini test etmek ve akademik araştırma sorularına yanıt vermek için tasarlanmıştır.
